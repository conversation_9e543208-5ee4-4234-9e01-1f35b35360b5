<template>
  <c-col col="12" lg="12">
    <filter-data @getLocationDiscrepancy="filter"/>

    <!-- Loading State -->
    <c-card v-if="isLoading">
      <c-card-body class="text-center">
        <c-spinner size="lg" class="mb-3"/>
        <h5>Analyzing location discrepancies...</h5>
        <p class="text-muted">This may take a few moments for large datasets.</p>
      </c-card-body>
    </c-card>

    <!-- Summary Statistics Section -->
    <c-card v-if="summaryStats && !isLoading">
      <c-card-header>
        <h4 class="text-center">
          Location Discrepancy Analysis Summary
          <br/>
          <small class="text-muted">
            From: {{ filterData.from_date }} to: {{ filterData.to_date }}
            | Distance Threshold: {{ filterData.distance_threshold }}m
          </small>
        </h4>
      </c-card-header>
      <c-card-body>
        <div class="row">
          <div class="col-lg-3 col-md-6 col-sm-12">
            <div class="card bg-primary text-white">
              <div class="card-body text-center">
                <h3>{{ summaryStats.total_accounts_analyzed }}</h3>
                <p class="mb-0">Total Accounts Analyzed</p>
              </div>
            </div>
          </div>
          <div class="col-lg-3 col-md-6 col-sm-12">
            <div class="card bg-warning text-white">
              <div class="card-body text-center">
                <h3>{{ summaryStats.accounts_with_discrepancies }}</h3>
                <p class="mb-0">Accounts with Discrepancies</p>
              </div>
            </div>
          </div>
          <div class="col-lg-3 col-md-6 col-sm-12">
            <div class="card bg-info text-white">
              <div class="card-body text-center">
                <h3>{{ summaryStats.total_visits_analyzed }}</h3>
                <p class="mb-0">Total Visits Analyzed</p>
              </div>
            </div>
          </div>
          <div class="col-lg-3 col-md-6 col-sm-12">
            <div class="card bg-success text-white">
              <div class="card-body text-center">
                <h3>{{ summaryStats.average_distance_variance }}m</h3>
                <p class="mb-0">Avg Distance Variance</p>
              </div>
            </div>
          </div>
        </div>
        <div class="row mt-3">
          <div class="col-lg-4 col-md-6 col-sm-12">
            <div class="card bg-danger text-white">
              <div class="card-body text-center">
                <h3>{{ summaryStats.maximum_distance_variance }}m</h3>
                <p class="mb-0">Max Distance Variance</p>
              </div>
            </div>
          </div>
          <div class="col-lg-4 col-md-6 col-sm-12">
            <div class="card bg-secondary text-white">
              <div class="card-body text-center">
                <h3>{{ summaryStats.accounts_without_registered_location }}</h3>
                <p class="mb-0">No Registered Location</p>
              </div>
            </div>
          </div>
          <div class="col-lg-4 col-md-6 col-sm-12">
            <div class="card bg-dark text-white">
              <div class="card-body text-center">
                <h3>{{ summaryStats.accounts_without_visits }}</h3>
                <p class="mb-0">No Visits in Period</p>
              </div>
            </div>
          </div>
        </div>
      </c-card-body>
    </c-card>

    <!-- Data Table Section -->
    <c-card v-if="reportData.length > 0 && !isLoading">
      <c-card-header>
        <div class="d-flex justify-content-between align-items-center">
          <h4>Account-Level Analysis Results</h4>
          <div class="d-flex align-items-center">
            <span v-if="selectedItems.length > 0" class="badge badge-info mr-2">
              {{ selectedItems.length }} selected
            </span>
            <span v-else class="text-muted mr-2">
              Select accounts to analyze discrepancies
            </span>
            <c-button
              v-if="selectedItems.length > 0"
              color="primary"
              size="sm"
              @click="analyzeSelectedAccountsDiscrepancies"
              class="mr-2"
            >
              <c-icon name="cil-chart-line"/>
              Analyze Selected Discrepancies
            </c-button>
          </div>
        </div>
      </c-card-header>
      <c-card-body>
        <!-- Vue2DataTable for Performance -->
        <Vue2DataTable
          :columns="tableColumns"
          :data-source="reportData"
          :show-pagination="true"
          :show-search="true"
          :show-total-bar="true"
          :row-height="60"
          :column-width="200"
          :selectable="true"
          :virtual-scroll-threshold="1000"
          search-placeholder="Search accounts..."
          @search="handleSearch"
          @selection-change="handleSelectionChange"
        >
          <!-- Custom slot for has_discrepancies column -->
          <template #has_discrepancies="{ value }">
            <span :class="value ? 'badge badge-danger' : 'badge badge-success'">
              {{ value ? 'Yes' : 'No' }}
            </span>
          </template>

          <!-- Custom slot for max_distance column -->
          <template #max_distance_from_registered_location="{ value }">
            <span :class="getDistanceClass(value)">
              {{ value ? value + 'm' : 'N/A' }}
            </span>
          </template>

          <!-- Custom slot for registered_location column -->
          <template #registered_location="{ value }">
            <span v-if="value && value.latitude && value.longitude" class="text-success">
              {{ value.latitude.toFixed(6) }}, {{ value.longitude.toFixed(6) }}
            </span>
            <span v-else class="text-muted">Not Set</span>
          </template>

          <!-- Custom slot for actions -->
          <template #actions="{ item }">
            <c-button
              v-if="item.visits_count > 0"
              color="info"
              size="sm"
              @click="toggleVisitDetails(item)"
              class="mr-1"
            >
              <c-icon name="cil-list"/>
              {{ expandedRows.includes(item.account_id) ? 'Hide' : 'Show' }} Visits
            </c-button>
            <c-button
              v-if="item.registered_location.has_location"
              color="primary"
              size="sm"
              @click="showOnMap(item)"
            >
              <c-icon name="cil-location-pin"/>
              Map
            </c-button>
          </template>
        </Vue2DataTable>
      </c-card-body>
      <c-card-footer>
        <download
          @getPrint="print"
          @getxlsx="download"
          @getpdf="createPDF"
          @getcsv="downloadCsv"
          :fields="exportFields"
          :data="exportData"
          :name="reportName"
        />
      </c-card-footer>
    </c-card>

    <!-- Selected Accounts Analysis Results -->
    <c-card v-if="analysisResults && !isLoading" class="mt-3">
      <c-card-header>
        <h4>
          <c-icon name="cil-chart-line" class="mr-2"/>
          Selected Accounts Discrepancy Analysis
        </h4>
      </c-card-header>
      <c-card-body>
        <div class="row">
          <div class="col-lg-4 col-md-6 col-sm-12">
            <div class="card bg-info text-white">
              <div class="card-body text-center">
                <h3>{{ analysisResults.selectedAccountsCount }}</h3>
                <p class="mb-0">Selected Accounts with Discrepancies</p>
              </div>
            </div>
          </div>
          <div class="col-lg-4 col-md-6 col-sm-12">
            <div class="card bg-warning text-white">
              <div class="card-body text-center">
                <h3>{{ analysisResults.totalVisitsAnalyzed }}</h3>
                <p class="mb-0">Total Visits Analyzed</p>
              </div>
            </div>
          </div>
          <div class="col-lg-4 col-md-6 col-sm-12">
            <div class="card bg-success text-white">
              <div class="card-body text-center">
                <h3>{{ analysisResults.maxFrequencyLocation ? analysisResults.maxFrequencyLocation.totalFrequency : 0 }}</h3>
                <p class="mb-0">Max Location Frequency</p>
              </div>
            </div>
          </div>
        </div>

        <div v-if="analysisResults.maxFrequencyLocation" class="row mt-3">
          <div class="col-12">
            <div class="card">
              <div class="card-header">
                <h5>
                  <c-icon name="cil-location-pin" class="mr-2"/>
                  Location with Maximum Frequency
                </h5>
              </div>
              <div class="card-body">
                <div class="row">
                  <div class="col-md-6">
                    <p><strong>Coordinates:</strong>
                      {{ analysisResults.maxFrequencyLocation.latitude.toFixed(6) }},
                      {{ analysisResults.maxFrequencyLocation.longitude.toFixed(6) }}
                    </p>
                    <p><strong>Total Frequency:</strong> {{ analysisResults.maxFrequencyLocation.totalFrequency }}</p>
                    <p><strong>Number of Visits:</strong> {{ analysisResults.maxFrequencyLocation.visits.length }}</p>
                  </div>
                  <div class="col-md-6">
                    <p><strong>Accounts Involved:</strong> {{ analysisResults.maxFrequencyLocation.accountsCount }}</p>
                    <p><strong>Max Distance from Registered:</strong> {{ analysisResults.maxFrequencyLocation.maxDistance }}m</p>
                    <p><strong>Visits Exceeding Threshold:</strong> {{ analysisResults.maxFrequencyLocation.exceedsThresholdCount }}</p>
                  </div>
                </div>
                <div class="mt-3">
                  <c-button
                    color="primary"
                    size="sm"
                    @click="showMaxFrequencyLocationOnMap"
                    class="mr-2"
                  >
                    <c-icon name="cil-map"/>
                    Show on Map
                  </c-button>
                  <c-button
                    color="info"
                    size="sm"
                    @click="exportAnalysisResults"
                  >
                    <c-icon name="cil-cloud-download"/>
                    Export Analysis
                  </c-button>
                </div>
              </div>
            </div>
          </div>
        </div>
      </c-card-body>
    </c-card>

    <!-- Expandable Visit Details -->
    <c-card v-for="accountId in expandedRows" :key="'visits-' + accountId" class="mt-3">
      <c-card-header>
        <h5>Visit Details for Account: {{ getAccountName(accountId) }}</h5>
      </c-card-header>
      <c-card-body>
        <Vue2DataTable
          :columns="visitColumns"
          :data-source="getVisitDetails(accountId)"
          :show-search="true"
          :show-pagination="true"
          :row-height="50"
          search-placeholder="Search visits..."
        >
          <!-- Custom slot for distance_from_registered_location -->
          <template #distance_from_registered_location="{ value }">
            <span :class="getDistanceClass(value)">
              {{ value ? value + 'm' : 'N/A' }}
            </span>
          </template>

          <!-- Custom slot for exceeds_threshold -->
          <template #exceeds_threshold="{ value }">
            <span :class="value ? 'badge badge-danger' : 'badge badge-success'">
              {{ value ? 'Yes' : 'No' }}
            </span>
          </template>

          <!-- Custom slot for visit_location -->
          <template #visit_location="{item}">
            <span v-if="item && item.latitude && item.longitude" class="text-info">
              {{ item.latitude.toFixed(6) }}, {{ item.longitude.toFixed(6) }}
            </span>
            <span v-else class="text-muted">Not Available</span>
          </template>
          <template #user_name="{item}">
            <span class="text-info">
              {{ item.user.name }}
            </span>
          </template>
        </Vue2DataTable>
      </c-card-body>
    </c-card>

    <!-- No Data Message -->
    <c-card v-if="!isLoading && reportData.length === 0 && hasSearched">
      <c-card-body class="text-center">
        <c-icon name="cil-info" size="3xl" class="text-muted mb-3"/>
        <h4 class="text-muted">No Data Found</h4>
        <p class="text-muted">
          No location discrepancy data found for the selected criteria.
          Try adjusting your filters or date range.
        </p>
      </c-card-body>
    </c-card>
  </c-col>
</template>

<script>
import filterData from "../../components/reports/LocationDiscrepancyAnalysis/filterData.vue";
import download from "../../components/download-reports/download.vue";
import Vue2DataTable from "../../components/common/Vue2DataTable/components/core/Vue2DataTable.vue";
import {Amiri} from "../../assets/fonts/Amiri-Regular-normal";
import jsPDF from "jspdf";
import "jspdf-autotable";
import LargeDatasetHandler from "../../mixins/LargeDatasetHandler.js";
import moment from "moment";


export default {
  mixins: [LargeDatasetHandler],
  components: {
    filterData,
    download,
    Vue2DataTable,
  },
  data() {
    return {
      isLoading: false,
      hasSearched: false,
      reportData: [],
      summaryStats: null,
      filterData: {},
      expandedRows: [],
      selectedItems: [],
      analysisResults: null,
      reportName: "Location Discrepancy Analysis Report",

      // Table columns for main data
      tableColumns: [
        {key: 'account_id', label: 'Account ID', sortable: true},
        {key: 'account_name', label: 'Account Name', sortable: true,},
        {key: 'account_code', label: 'Account Code', sortable: true,},
        {key: 'account_address', label: 'Address', sortable: true,},
        {key: 'registered_location', label: 'Registered Location', sortable: false,},
        {key: 'visits_count', label: 'Visits Count', sortable: true,},
        {key: 'has_discrepancies', label: 'Has Discrepancies', sortable: true,},
        {key: 'max_distance_from_registered', label: 'Max Distance', sortable: true,},
        {key: 'distance_threshold_used', label: 'Threshold Used', sortable: true,},
        {key: 'actions', label: 'Actions', sortable: false,},
      ],


      // Table columns for visit details
      visitColumns: [
        {key: 'visit_id', label: 'Visit ID', sortable: true},
        {key: 'visit_date', label: 'Visit Date', sortable: true},
        {
          key: 'visit_location',
          label: 'Visit Location',
          sortable: false,

        },
        {key: 'user_name', label: 'User Name', sortable: true},
        {key: 'visit_frequency', label: 'Frequency', sortable: true},
        {key: 'distance_from_registered', label: 'Distance', sortable: true},
        {key: 'exceeds_threshold', label: 'Exceeds Threshold', sortable: true},
      ],
      parameters: {
        distance_threshold: 0,
        div_ids: [],
        from_date: "",
        line_ids: [],
        recent_visits_limit: 0,
        to_date: "",
      },
    };
  },
  computed: {
    exportFields() {
      // Combine account columns (excluding actions) with visit columns
      const accountFields = this.tableColumns
        .filter(col => col.key !== 'actions')
        .map(col => col.key);

      const visitFields = this.visitColumns.map(col => col.key);

      return [...accountFields, ...visitFields];
    },

    exportData() {
      const exportData = [];

      this.reportData.forEach(item => {
        // Format registered_location for export
        let formattedRegisteredLocation;
        if (item.registered_location && item.registered_location.latitude) {
          formattedRegisteredLocation = `${item.registered_location.latitude}, ${item.registered_location.longitude}`;
        } else {
          formattedRegisteredLocation = 'Not Set';
        }

        // If the account has visit locations, create a row for each visit
        if (item.visit_locations && item.visit_locations.length > 0) {
          item.visit_locations.forEach(visit => {
            const exportItem = {
              // Account data
              account_id: item.account_id,
              account_name: item.account_name,
              account_code: item.account_code,
              account_address: item.account_address,
              registered_location: formattedRegisteredLocation,
              visits_count: item.visits_count,
              has_discrepancies: item.has_discrepancies,
              max_distance_from_registered: item.max_distance_from_registered,
              distance_threshold_used: item.distance_threshold_used,

              // Visit data
              visit_id: visit.visit_id,
              visit_date: visit.visit_date,
              visit_location: visit.latitude && visit.longitude ? `${visit.latitude}, ${visit.longitude}` : 'N/A',
              user_name: visit.user.name,
              visit_frequency: visit.visit_frequency,
              distance_from_registered: visit.distance_from_registered,
              exceeds_threshold: visit.exceeds_threshold
            };

            exportData.push(exportItem);
          });
        } else {
          // If no visits, create a single row with account data and empty visit fields
          const exportItem = {
            // Account data
            account_id: item.account_id,
            account_name: item.account_name,
            account_code: item.account_code,
            account_address: item.account_address,
            registered_location: formattedRegisteredLocation,
            visits_count: item.visits_count,
            has_discrepancies: item.has_discrepancies,
            max_distance_from_registered: item.max_distance_from_registered,
            distance_threshold_used: item.distance_threshold_used,

            // Empty visit data
            visit_id: '',
            visit_date: '',
            visit_location: '',
            user_name: '',
            visit_frequency: '',
            distance_from_registered: '',
            exceeds_threshold: ''
          };

          exportData.push(exportItem);
        }
      });

      return exportData;
    },
    duration() {
      if (!this.filterData.from_date || !this.filterData.to_date) {
        return [[], null];
      }

      const fromDate = moment(this.filterData.from_date);
      const toDate = moment(this.filterData.to_date);

      if (!fromDate.isValid() || !toDate.isValid()) {
        return [[], null];
      }

      // Ensure fromDate is not after toDate
      if (fromDate.isAfter(toDate)) {
        return [[], null];
      }

      const months = [];
      const currentDate = fromDate.clone().startOf('month');
      const endDate = toDate.clone().startOf('month');

      // Get the year from the from_date
      const year = fromDate.year();

      while (currentDate.isSameOrBefore(endDate)) {
        months.push(currentDate.format('MMMM')); // Full month name (January, February, etc.)
        currentDate.add(1, 'month');
      }

      return [months, year];
    }

  },
  emits: ["downloaded"],
  methods: {
    async filter({filters}) {
      this.filterData = filters;
      this.hasSearched = true;
      this.expandedRows = [];
      // Note: Don't reset selectedItems here as Vue2DataTable manages its own selection
      this.analysisResults = null;

      // Initialize large dataset processing
      if (!this.initializeLargeDatasetProcessing('Analyzing Location Discrepancies')) {
        return;
      }

      this.isLoading = true;

      try {
        // Update progress
        this.updateLargeDatasetProgress(10, 'Sending request to server...');

        const {res: response, error} = await this.tryCatch(
          axios.post("/api/account-location-comparison", filters)
        );

        if (error) {
          this.handleLargeDatasetError(error, 'API request');
          return;
        }

        this.updateLargeDatasetProgress(50, 'Processing server response...');

        if (response.data.data.success) {
          this.summaryStats = response.data.data.summary;

          this.parameters = response.data.data.parameters;

          // Check if dataset is large and warn user
          const accountCount = response.data.data.accounts.length;
          if (accountCount > 10000) {
            if (!this.shouldProceedWithLargeDataset(accountCount, 'location analysis')) {
              this.finalizeLargeDatasetProcessing();
              this.isLoading = false;
              return;
            }
          }

          this.updateLargeDatasetProgress(80, 'Loading data into table...');

          // Process data in chunks for large datasets
          if (accountCount > 5000) {
            await this.processLargeDataset(response.data.data.accounts);
          } else {
            this.reportData = response.data.data.accounts;
          }

          this.updateLargeDatasetProgress(100, 'Analysis complete');

          // Show success message with statistics
          this.flash(
            `Analysis complete: ${accountCount} accounts analyzed, ${this.summaryStats.accounts_with_discrepancies} with discrepancies`,
            'success'
          );

        } else {
          this.handleLargeDatasetError(
            new Error("Failed to generate report"),
            'server response'
          );
        }
      } catch (error) {
        this.handleLargeDatasetError(error, 'location analysis');
      } finally {
        this.finalizeLargeDatasetProcessing();
        this.isLoading = false;
      }
    },

    async processLargeDataset(accounts) {
      try {
        // Process accounts in chunks to prevent UI blocking
        const chunkSize = 1000;
        this.reportData = [];

        for (let i = 0; i < accounts.length; i += chunkSize) {
          const chunk = accounts.slice(i, i + chunkSize);
          this.reportData.push(...chunk);

          // Update progress
          const progress = 80 + (20 * (i + chunkSize) / accounts.length);
          this.updateLargeDatasetProgress(progress, `Loading ${i + chunkSize}/${accounts.length} accounts...`);

          // Allow UI to update
          await this.$nextTick();

          // Small delay to prevent browser freezing
          if (i > 0 && i % 5000 === 0) {
            await new Promise(resolve => setTimeout(resolve, 100));
          }
        }
      } catch (error) {
        throw new Error(`Failed to process large dataset: ${error.message}`);
      }
    },
    handleSearch(payload) {
      // Vue2DataTable sends a payload object with searchTerm and results
      this.$emit('search', payload.searchTerm || payload);
    },

    handleSelectionChange(selectedItems) {
      this.selectedItems = selectedItems;
      console.log('Selection changed:', selectedItems);

      // Keep analysis results visible even when selection changes
      // Analysis results will only be cleared when a new analysis is performed or data is refreshed
    },

    analyzeSelectedAccountsDiscrepancies() {
      if (this.selectedItems.length === 0) {
        this.flash('Please select accounts to analyze', 'warning');
        return;
      }

      // Filter selected accounts that have discrepancies
      const selectedAccountsWithDiscrepancies = this.selectedItems.filter(account =>
        account.has_discrepancies === true || account.has_discrepancies === 1
      );

      if (selectedAccountsWithDiscrepancies.length === 0) {
        this.flash(`No selected accounts have location discrepancies. Selected ${this.selectedItems.length} accounts, but none have discrepancies.`, 'info');
        return;
      }

      // Collect all visit locations from selected accounts with discrepancies
      const allVisitLocations = [];
      selectedAccountsWithDiscrepancies.forEach(account => {
        if (account.visit_locations && account.visit_locations.length > 0) {
          account.visit_locations.forEach(visit => {
            if (visit.latitude && visit.longitude) {
              allVisitLocations.push({
                latitude: visit.latitude,
                longitude: visit.longitude,
                account_id: account.account_id,
                account_name: account.account_name,
                visit_id: visit.visit_id,
                visit_date: visit.visit_date,
                visit_frequency: visit.visit_frequency || 1,
                distance_from_registered: visit.distance_from_registered,
                exceeds_threshold: visit.exceeds_threshold
              });
            }
          });
        }
      });

      if (allVisitLocations.length === 0) {
        this.flash('No visit location data found for selected accounts with discrepancies', 'warning');
        return;
      }

      // Find location with maximum frequency
      const locationWithMaxFrequency = this.findLocationWithMaxFrequency(allVisitLocations);

      // Store results and display
      this.analysisResults = {
        selectedAccountsCount: selectedAccountsWithDiscrepancies.length,
        totalVisitsAnalyzed: allVisitLocations.length,
        maxFrequencyLocation: locationWithMaxFrequency,
        selectedAccounts: selectedAccountsWithDiscrepancies,
        allVisitLocations: allVisitLocations
      };

      this.displayDiscrepancyAnalysisResults(selectedAccountsWithDiscrepancies, locationWithMaxFrequency, allVisitLocations);
    },

    findLocationWithMaxFrequency(visitLocations) {
      // Group locations by coordinates (with small tolerance for GPS variations)
      const locationGroups = {};
      const tolerance = 0.0001; // ~11 meters tolerance

      visitLocations.forEach(visit => {
        // Round coordinates to create location groups
        const roundedLat = Math.round(visit.latitude / tolerance) * tolerance;
        const roundedLng = Math.round(visit.longitude / tolerance) * tolerance;
        const locationKey = `${roundedLat},${roundedLng}`;

        if (!locationGroups[locationKey]) {
          locationGroups[locationKey] = {
            latitude: roundedLat,
            longitude: roundedLng,
            visits: [],
            totalFrequency: 0,
            accountsCount: new Set(),
            maxDistance: 0,
            exceedsThresholdCount: 0
          };
        }

        locationGroups[locationKey].visits.push(visit);
        locationGroups[locationKey].totalFrequency += visit.visit_frequency;
        locationGroups[locationKey].accountsCount.add(visit.account_id);
        locationGroups[locationKey].maxDistance = Math.max(
          locationGroups[locationKey].maxDistance,
          visit.distance_from_registered || 0
        );

        if (visit.exceeds_threshold) {
          locationGroups[locationKey].exceedsThresholdCount++;
        }
      });

      // Find location with maximum frequency
      let maxFrequencyLocation = null;
      let maxFrequency = 0;

      Object.values(locationGroups).forEach(location => {
        if (location.totalFrequency > maxFrequency) {
          maxFrequency = location.totalFrequency;
          maxFrequencyLocation = {
            ...location,
            accountsCount: location.accountsCount.size
          };
        }
      });

      return maxFrequencyLocation;
    },

    displayDiscrepancyAnalysisResults(selectedAccounts, maxFrequencyLocation, allVisitLocations) {
      const accountsCount = selectedAccounts.length;
      const totalVisits = allVisitLocations.length;

      let message = `Analysis of ${accountsCount} selected accounts with discrepancies:\n\n`;
      message += `• Total visits analyzed: ${totalVisits}\n`;

      if (maxFrequencyLocation) {
        message += `• Location with highest frequency:\n`;
        message += `  - Coordinates: ${maxFrequencyLocation.latitude.toFixed(6)}, ${maxFrequencyLocation.longitude.toFixed(6)}\n`;
        message += `  - Total frequency: ${maxFrequencyLocation.totalFrequency}\n`;
        message += `  - Number of visits: ${maxFrequencyLocation.visits.length}\n`;
        message += `  - Accounts involved: ${maxFrequencyLocation.accountsCount}\n`;
        message += `  - Max distance from registered: ${maxFrequencyLocation.maxDistance}m\n`;
        message += `  - Visits exceeding threshold: ${maxFrequencyLocation.exceedsThresholdCount}\n`;

        // Note: Map display is now manual - user can click "Show on Map" button to view locations
      }

      this.flash(message, 'success');

      // Log detailed results for debugging
      console.log('Selected accounts with discrepancies:', selectedAccounts);
      console.log('Location with max frequency:', maxFrequencyLocation);
      console.log('All visit locations:', allVisitLocations);
    },

    showMaxFrequencyLocationOnMap() {
      if (!this.analysisResults || !this.analysisResults.maxFrequencyLocation) {
        this.flash('No analysis results available', 'warning');
        return;
      }

      const maxFreqLocation = this.analysisResults.maxFrequencyLocation;

      const mapPositions = [
        {
          lat: Number(maxFreqLocation.latitude),
          lng: Number(maxFreqLocation.longitude),
          title: `Max Frequency Location (${maxFreqLocation.totalFrequency} total frequency)`,
          type: 'max_frequency'
        },
        ...maxFreqLocation.visits.map(visit => ({
          lat: Number(visit.latitude),
          lng: Number(visit.longitude),
          title: `${visit.account_name} - Visit ${visit.visit_id} (${visit.visit_date})`,
          type: 'visit'
        }))
      ];

      this.$root.$map('Location with Maximum Frequency Analysis', mapPositions);
    },

    exportAnalysisResults() {
      if (!this.analysisResults) {
        this.flash('No analysis results to export', 'warning');
        return;
      }

      try {
        // Prepare export data
        const exportData = [];

        // Add summary row
        exportData.push({
          type: 'Summary',
          account_id: '',
          account_name: 'ANALYSIS SUMMARY',
          selected_accounts_count: this.analysisResults.selectedAccountsCount,
          total_visits_analyzed: this.analysisResults.totalVisitsAnalyzed,
          max_frequency_location_lat: this.analysisResults.maxFrequencyLocation?.latitude || '',
          max_frequency_location_lng: this.analysisResults.maxFrequencyLocation?.longitude || '',
          max_frequency_total: this.analysisResults.maxFrequencyLocation?.totalFrequency || 0,
          max_frequency_visits_count: this.analysisResults.maxFrequencyLocation?.visits.length || 0,
          max_frequency_accounts_count: this.analysisResults.maxFrequencyLocation?.accountsCount || 0,
          max_distance_from_registered: this.analysisResults.maxFrequencyLocation?.maxDistance || 0,
          exceeds_threshold_count: this.analysisResults.maxFrequencyLocation?.exceedsThresholdCount || 0
        });

        // Add detailed visit data
        this.analysisResults.allVisitLocations.forEach(visit => {
          exportData.push({
            type: 'Visit Detail',
            account_id: visit.account_id,
            account_name: visit.account_name,
            visit_id: visit.visit_id,
            visit_date: visit.visit_date,
            visit_latitude: visit.latitude,
            visit_longitude: visit.longitude,
            visit_frequency: visit.visit_frequency,
            distance_from_registered: visit.distance_from_registered,
            exceeds_threshold: visit.exceeds_threshold ? 'Yes' : 'No',
            is_max_frequency_location: this.isVisitAtMaxFrequencyLocation(visit) ? 'Yes' : 'No'
          });
        });

        // Download as Excel
        const fileName = `Selected_Accounts_Discrepancy_Analysis_${moment().format('YYYY-MM-DD_HH-mm-ss')}.xlsx`;
        this.downloadXlsx(exportData, fileName);

        this.flash('Analysis results exported successfully', 'success');
      } catch (error) {
        this.showErrorMessage(error);
      }
    },

    isVisitAtMaxFrequencyLocation(visit) {
      if (!this.analysisResults?.maxFrequencyLocation) return false;

      const tolerance = 0.0001;
      const maxFreqLocation = this.analysisResults.maxFrequencyLocation;

      return Math.abs(visit.latitude - maxFreqLocation.latitude) < tolerance &&
             Math.abs(visit.longitude - maxFreqLocation.longitude) < tolerance;
    },

    toggleVisitDetails(item) {
      const accountId = item.account_id;
      const index = this.expandedRows.indexOf(accountId);

      if (index > -1) {
        this.expandedRows.splice(index, 1);
      } else {
        this.expandedRows.push(accountId);
      }
    },

    getVisitDetails(accountId) {
      const account = this.reportData.find(item => item.account_id === accountId);
      return account ? account.visit_locations : [];
    },

    getAccountName(accountId) {
      const account = this.reportData.find(item => item.account_id === accountId);
      return account ? account.account_name : 'Unknown Account';
    },

    getDistanceClass(distance) {
      if (!distance) return 'text-muted';
      if (distance > this.filterData.distance_threshold) return 'text-danger font-weight-bold';
      if (distance > this.filterData.distance_threshold * 0.8) return 'text-warning';
      return 'text-success';
    },

    showOnMap(item) {
      if (item.registered_location && item.visit_locations) {
        const positions = [
          {
            lat: Number(item.registered_location.latitude),
            lng: Number(item.registered_location.longitude),
            title: 'Registered Location',
            type: 'registered'
          },
          ...item.visit_locations.map(visit => ({
            lat: Number(visit.latitude),
            lng: Number(visit.longitude),
            title: `Visit ${visit.visit_id} - ${visit.visit_date}`,
            type: 'visit'
          }))
        ];

        this.$root.$map(`Location Analysis for ${item.account_name}`, positions);
      }
    },

    print() {
      this.$htmlToPaper("print");
    },

    async download() {
      try {
        if (!this.shouldProceedWithLargeDataset(this.reportData.length, 'Excel export')) {
          return;
        }

        if (!this.initializeLargeDatasetProcessing('Excel Export')) {
          return;
        }

        this.isLoading = true;
        this.updateLargeDatasetProgress(10, 'Preparing Excel export...');

        // Prepare data with error handling
        const headers = this.exportData.length > 0 ? Object.keys(this.exportData[0]) : [];
        if (headers.length === 0) {
          throw new Error('No column headers found in data');
        }

        // For very large datasets (>10k records), use chunked processing
        if (this.reportData.length > 10000) {
          this.initializeLargeDatasetProcessing("Preparing download");
        }

        this.updateLargeDatasetProgress(25, 'Generating Excel file...');


        this.updateLargeDatasetProgress(50, 'Generating Excel file');


        await this.downloadStyledExcel(
          this.duration,
          this.exportData,
          headers,
          this.reportName,
          ''
        );

        this.updateLargeDatasetProgress(100, 'Excel export complete');


        this.flash('Excel export completed successfully', 'success');
        this.$emit("downloaded");

      } catch (error) {
        this.handleLargeDatasetError(error, 'Excel export');
      } finally {
        this.finalizeLargeDatasetProcessing();
        this.isLoading = false;
      }
    },

    async downloadLargeDataset() {
      try {
        // Process data in chunks to prevent memory issues
        const chunkSize = 5000;
        const chunks = [];

        this.updateLargeDatasetProgress(20, 'Splitting data into chunks...');

        for (let i = 0; i < this.exportData.length; i += chunkSize) {
          chunks.push(this.exportData.slice(i, i + chunkSize));
        }

        // Create separate files for very large datasets to prevent browser crashes
        if (chunks.length > 1) {
          this.updateLargeDatasetProgress(30, `Preparing ${chunks.length} Excel files...`);

          for (let i = 0; i < chunks.length; i++) {
            const fileName = `${this.reportName}_Part${i + 1}_of_${chunks.length}.xlsx`;

            this.updateLargeDatasetProgress(
              30 + (60 * (i + 1) / chunks.length),
              `Generating file ${i + 1} of ${chunks.length}...`
            );

            this.downloadXlsx(chunks[i], fileName);

            // Add small delay between downloads to prevent browser overload
            await new Promise(resolve => setTimeout(resolve, 500));
          }

          this.flash(`Dataset exported in ${chunks.length} files due to size`, 'success');
        } else {
          this.updateLargeDatasetProgress(50, 'Generating single Excel file...');
          this.downloadXlsx(this.exportData, `${this.reportName}.xlsx`);
        }

        this.updateLargeDatasetProgress(100, 'Excel export complete');

      } catch (error) {
        throw new Error(`Large dataset export failed: ${error.message}`);
      }
    },

    async downloadCsv() {
      try {
        // Show loading state for large datasets
        if (this.reportData.length > 1000) {
          this.isLoading = true;
          this.$toast.info('Preparing CSV export for large dataset...', {
            duration: 3000
          });
        }

        // For very large datasets, use chunked processing
        if (this.reportData.length > 10000) {
          await this.downloadLargeDatasetCsv();
        } else {
          this.downloadXlsx(this.exportData, `${this.reportName}.csv`);
        }

        this.$emit("downloaded");
      } catch (error) {
        this.showErrorMessage(error);
      } finally {
        this.isLoading = false;
      }
    },

    async downloadLargeDatasetCsv() {
      const chunkSize = 10000; // CSV can handle larger chunks
      const chunks = [];

      for (let i = 0; i < this.exportData.length; i += chunkSize) {
        chunks.push(this.exportData.slice(i, i + chunkSize));
      }

      if (chunks.length > 1) {
        for (let i = 0; i < chunks.length; i++) {
          const fileName = `${this.reportName}_Part${i + 1}_of_${chunks.length}.csv`;
          this.downloadXlsx(chunks[i], fileName);

          await new Promise(resolve => setTimeout(resolve, 300));
        }

        this.$toast.success(`CSV exported in ${chunks.length} files due to size`, {
          duration: 5000
        });
      } else {
        this.downloadXlsx(this.exportData, `${this.reportName}.csv`);
      }
    },

    createPDF() {
      const pdfName = "LocationDiscrepancyAnalysisReport";
      const columns = [
        {title: "Account ID", dataKey: "account_id"},
        {title: "Account Name", dataKey: "account_name"},
        {title: "Account Code", dataKey: "account_code"},
        {title: "Address", dataKey: "account_address"},
        {title: "Registered Location", dataKey: "registered_location"},
        {title: "Visits Count", dataKey: "visits_count"},
        {title: "Has Discrepancies", dataKey: "has_discrepancies"},
        {title: "Max Distance", dataKey: "max_distance_from_registered_location"},
        {title: "Threshold Used", dataKey: "distance_threshold_used"},
      ];

      const body = this.exportData;
      const doc = new jsPDF({filters: ["ASCIIHexEncode"]});

      doc.addFileToVFS("Amiri-Regular.ttf", Amiri);
      doc.addFont("Amiri-Regular.ttf", "Amiri", "normal");
      doc.setFont("Amiri");
      doc.setFontSize(10);

      // Add title
      doc.setFontSize(16);
      doc.text("Location Discrepancy Analysis Report", 14, 20);
      doc.setFontSize(12);
      doc.text(`Period: ${this.filterData.from_date} to ${this.filterData.to_date}`, 14, 30);
      doc.text(`Distance Threshold: ${this.filterData.distance_threshold}m`, 14, 40);

      doc.autoTable({
        columns,
        body,
        margin: {top: 50},
        showHead: "firstPage",
        styles: {
          lineColor: "#c7c7c7",
          lineWidth: 0,
          cellPadding: 2,
          font: "Amiri",
        },
      });

      doc.save(pdfName + ".pdf");
    },
  },
};
</script>

<style scoped>
.table-class > thead > tr > th {
  background-color: #005cc8;
  color: white;
  text-align: center;
  position: sticky;
  top: 0;
}

.badge {
  font-size: 0.75em;
}

.card {
  margin-bottom: 1rem;
}

.card-body h3 {
  margin-bottom: 0.5rem;
}

.text-danger.font-weight-bold {
  font-weight: 700 !important;
}
</style>
